# n8n Docker Setup

This folder contains the Docker configuration to run n8n locally.

## Quick Start

1. **Start n8n:**
   ```bash
   docker-compose up -d
   ```

2. **Access n8n:**
   - Open your browser and go to: http://localhost:5678
   - Default credentials:
     - Username: `admin`
     - Password: `password`

3. **Stop n8n:**
   ```bash
   docker-compose down
   ```

## Configuration

### Authentication
- Basic authentication is enabled by default
- Default credentials are set in the `.env` file
- **Important:** Change the default password for production use!

### Data Persistence
- n8n data is stored in a Docker volume named `n8n_data`
- Workflows and credentials are also mapped to local folders for easy backup

### Environment Variables
You can modify the `.env` file to customize:
- Authentication credentials
- Database settings (SQLite is used by default)
- Timezone settings
- Webhook URLs

## Useful Commands

```bash
# Start n8n in the background
docker-compose up -d

# View logs
docker-compose logs -f n8n

# Stop n8n
docker-compose down

# Stop and remove all data (careful!)
docker-compose down -v

# Update n8n to latest version
docker-compose pull
docker-compose up -d
```

## Folders

- `workflows/` - Your n8n workflows will be stored here
- `credentials/` - Your n8n credentials will be stored here

## Security Notes

1. Change the default username and password in `.env`
2. For production, consider using HTTPS
3. Restrict access to the n8n port (5678) if needed
4. Regularly backup your workflows and credentials

## Troubleshooting

- If port 5678 is already in use, change it in `docker-compose.yml`
- Check logs with `docker-compose logs n8n`
- Ensure Docker is running before starting n8n
